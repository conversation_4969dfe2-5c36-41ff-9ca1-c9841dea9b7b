# Copyright 2023 PingCAP, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

## Mark targets to build sequentially
# Example: build_sequentially(a, b, c, d)
# a -> b -> c -> d
function(build_sequentially target1 target2)
    add_dependencies(${target2} ${target1})
    if(${ARGC} GREATER 2)
        build_sequentially(${target2} ${ARGN})
    endif()
endfunction()