name: Bump TiFlash Proxy

on:
  workflow_dispatch:
    inputs:
      proxy_commit:
        description: "Update to this TiFlash Proxy commit hash"
        required: true

permissions:
  contents: write
  pull-requests: write

jobs:
  bump-proxy:
    runs-on:
      - self-hosted
      - serverless
      - X64
    timeout-minutes: 20
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
          submodules: recursive
          fetch-depth: 0
          clean: false

      - name: Update tiflash-proxy submodule
        run: |
          git submodule update --init --recursive
          git submodule status
          cd contrib/tiflash-proxy
          git fetch origin
          git checkout ${{ github.event.inputs.proxy_commit }}

      - name: Create Pull Request
        id: cpr
        uses: peter-evans/create-pull-request@v5
        with:
          base: cloud-engine-on-release-8.5
          token: ${{ secrets.GIT_ACTION_BOT }}
          commit-message: "proxy: Bump to ${{ github.event.inputs.proxy_commit }}"
          committer: GitHub <<EMAIL>>
          author: ${{ github.actor }} <${{ github.actor }}@users.noreply.github.com>
          signoff: true
          delete-branch: true
          assignees: ${{ github.actor }}
          branch-suffix: short-commit-hash
          add-paths: |
            contrib/tiflash-proxy
          title: "proxy: Bump to ${{ github.event.inputs.proxy_commit }}"
          body: |
            Update proxy to https://github.com/tidbcloud/cloud-storage-engine/commits/${{ github.event.inputs.proxy_commit }}

            This PR is triggered by:
            - ${{ github.actor }}
          labels: |
            automated pr

      - name: Check outputs
        run: |
          echo "Pull Request Number - ${{ steps.cpr.outputs.pull-request-number }}"
          echo "Pull Request URL - ${{ steps.cpr.outputs.pull-request-url }}"

      - name: Enable Pull Request Automerge
        if: steps.cpr.outputs.pull-request-operation == 'created'
        uses: peter-evans/enable-pull-request-automerge@v1
        with:
          token: ${{ secrets.GIT_ACTION_BOT }}
          pull-request-number: ${{ steps.cpr.outputs.pull-request-number }}
          merge-method: squash

      - name: Approve the PR
        if: steps.cpr.outputs.pull-request-operation == 'created'
        uses: hmarr/auto-approve-action@v3
        with:
          pull-request-number: ${{ steps.cpr.outputs.pull-request-number }}

      - name: Back ping
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GIT_ACTION_BOT }}
          script: |
            const { data: commit } = (await github.rest.repos.getCommit({
              ref: "${{ github.event.inputs.proxy_commit }}",
              owner: "tidbcloud",
              repo: "cloud-storage-engine",
            }));

            if (!commit) {
              return;
            }

            // Resolve the PR number from commit title
            const messageTitle = commit.commit.message.split('\n')[0].trim();
            const prMatch = messageTitle.match(/\(#(\d+)\)$/);
            if (!prMatch) {
              return;
            }

            const prNumber = Number(prMatch[1]);

            const notifyBody = `
            :white_check_mark: Bump TiFlash proxy PR is created:

            ${{ steps.cpr.outputs.pull-request-url }}
            `;

            await github.rest.issues.createComment({
              issue_number: prNumber,
              owner: "tidbcloud",
              repo: "cloud-storage-engine",
              body: notifyBody,
            });
