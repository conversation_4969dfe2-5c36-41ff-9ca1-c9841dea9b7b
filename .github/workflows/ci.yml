name: CI

on:
  pull_request:
    branches: ["cloud-engine-on-release-8.5"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: Build
    strategy:
      fail-fast: true
      matrix:
        arch: [X64, ARM64]
    runs-on:
      - self-hosted
      - serverless # Note: Only repository runners has TiFlash build envs.
      - ${{ matrix.arch }}
    timeout-minutes: 60
    env:
      # GitHub Action supports color output
      CLICOLOR_FORCE: "1"
      CMAKE_COLOR_DIAGNOSTICS: ON
      CC: clang
      CXX: clang++
    steps:
      - name: Setup minio
        run: |
          mc alias set minio_server ${{ vars.MINIO_ENDPOINT }} ${{ secrets.MINIO_AK }} ${{ secrets.MINIO_SK }}

      - name: Checkout repository and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.GIT_ACTION_BOT }}
          fetch-depth: 0
          clean: false # Preserve build artifacts for reuse

      - name: Prepare TiFlash Proxy cache
        id: proxy_cache
        run: |
          pushd contrib/tiflash-proxy > /dev/null
          PROXY_COMMIT=$(git log -1 --format=%H)
          popd > /dev/null

          PROXY_CACHE_DIR=minio_server/tiflash-cse-ci/ci_tiflash_proxy_cache
          mc ls $PROXY_CACHE_DIR

          PROXY_CACHE_OBJECT=$PROXY_CACHE_DIR/$PROXY_COMMIT-$(uname -p)-debug.so
          echo "PROXY_CACHE_OBJECT=$PROXY_CACHE_OBJECT" | tee -a $GITHUB_OUTPUT

          rm -f /tmp/libtiflash_proxy.so
          mc cp $PROXY_CACHE_OBJECT /tmp/libtiflash_proxy.so || true
          if [ -f /tmp/libtiflash_proxy.so ]
          then
            # Cache exist, we will reuse the TiFlash proxy library.
            echo "USE_INTERNAL_TIFLASH_PROXY=OFF" | tee -a $GITHUB_OUTPUT
            mkdir -p contrib/tiflash-proxy/target/release
            cp /tmp/libtiflash_proxy.so contrib/tiflash-proxy/target/release/libtiflash_proxy.so
          else
            # Cache not exist, we will build a new TiFlash proxy library.
            echo "USE_INTERNAL_TIFLASH_PROXY=ON" | tee -a $GITHUB_OUTPUT
            rm -rf contrib/tiflash-proxy/target/release
            echo "::warning ::TiFlash proxy library cache not found, will build a new one."
          fi

      - name: Configure
        # First configure based on cache, if failed, retry without cache.
        run: |
          mkdir -p cmake-build-Debug
          cd cmake-build-Debug

          cmake .. -GNinja --fresh \
            -DCMAKE_BUILD_TYPE=DEBUG \
            -DCMAKE_C_COMPILER=clang \
            -DCMAKE_CXX_COMPILER=clang++ \
            -DDEBUG_WITHOUT_DEBUG_INFO=ON \
            -DENABLE_TESTS=ON \
            -DPREBUILT_LIBS_ROOT=$GITHUB_WORKSPACE/contrib/tiflash-proxy/ \
            -DUSE_INTERNAL_TIFLASH_PROXY=${{ steps.proxy_cache.outputs.USE_INTERNAL_TIFLASH_PROXY }}

      - name: Build
        run: |
          cd cmake-build-Debug
          cmake --build . --target gtests_dbms tiflash --parallel $(nproc)
          rm -rf artifacts
          cmake --install . --component=tiflash-gtest --prefix=artifacts
          ls -la artifacts/

      - name: Cache TiFlash Proxy
        if: steps.proxy_cache.outputs.USE_INTERNAL_TIFLASH_PROXY == 'ON'
        run: |
          mc cp cmake-build-Debug/contrib/tiflash-proxy-cmake/debug/libtiflash_proxy.so ${{ steps.proxy_cache.outputs.PROXY_CACHE_OBJECT }}

      - name: Upload Artifact
        if: matrix.arch == 'X64' # Only run unit tests on X64
        run: |
          mc cp ./cmake-build-Debug/artifacts/* minio_server/tiflash-cse-ci/ci_unit_test_artifact/${{ github.run_id }}/

  unit_test:
    name: Unit Test
    runs-on: [self-hosted, serverless, X64]
    needs: build
    timeout-minutes: 30
    strategy:
      max-parallel: 3
      fail-fast: false
      matrix:
        worker_id: [1, 2, 3]
    steps:
      - name: Checkout repository and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.GIT_ACTION_BOT }}
          fetch-depth: 0
          clean: false # Preserve build artifacts for reuse

      - name: Setup minio
        run: |
          mc alias set minio_server ${{ vars.MINIO_ENDPOINT }} ${{ secrets.MINIO_AK }} ${{ secrets.MINIO_SK }}

      - name: Download Artifact
        run: |
          mc cp --recursive minio_server/tiflash-cse-ci/ci_unit_test_artifact/${{ github.run_id }}/ /tmp/

      - name: Run Tests
        run: |
          chmod +x /tmp/gtests_dbms
          export FORCE_COLOR=1
          python3 tests/gtest_10x.py \
            --workers=24 --shard_count=3 --shard_index=${{ matrix.worker_id }} \
            --working_dir=/tmp/gtest_10x_output \
            /tmp/gtests_dbms -- \
            --gtest_catch_exceptions=1

      - name: Test Log (Stderr)
        if: failure()
        run: |
          LOG_PATH=minio_server/tiflash-cse-ci/ci_unit_test_logs/${{ github.run_id }}/test_stderr.log
          mc cp /tmp/gtest_10x_output/test_stderr.log $LOG_PATH
          echo "Download test log: ${{ vars.MINIO_ENDPOINT }}/$LOG_PATH"

      - name: Test Failure Details (Stdout)
        if: failure()
        run: |
          cat /tmp/gtest_10x_output/test_stdout.log

  cleanup:
    name: CI Cleanup
    runs-on: [self-hosted, serverless, X64]
    needs: [unit_test]
    steps:
      - name: Setup minio
        run: |
          mc alias set minio_server ${{ vars.MINIO_ENDPOINT }} ${{ secrets.MINIO_AK }} ${{ secrets.MINIO_SK }}

      - name: Clean up artifact
        if: success()
        run: |
          mc rm --recursive minio_server/tiflash-cse-ci/ci_unit_test_artifact/${{ github.run_id }}/ || true

  check_format:
    name: Check format
    runs-on: [self-hosted, serverless, X64]
    steps:
      - name: Checkout repository and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.GIT_ACTION_BOT }}
          fetch-depth: 0
          clean: false # Preserve build artifacts for reuse

      - name: Check format
        run: |
          merge_base=$(git merge-base origin/cloud-engine-on-release-8.5 HEAD)
          python3 format-diff.py --check_formatted --diff_from $merge_base

  unit_test_libclara:
    name: Unit Test (libclara)
    strategy:
      fail-fast: true
      matrix:
        arch: [X64, ARM64]
    runs-on:
      - self-hosted
      - serverless # Note: Only repository runners has TiFlash build envs.
      - ${{ matrix.arch }}
    timeout-minutes: 60
    env:
      # Needed because we don't have gcc in the base image
      CC: clang
      CXX: clang++
      RUSTFLAGS: -Dwarnings
    steps:
      - name: Checkout repository and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.GIT_ACTION_BOT }}
          fetch-depth: 0
          clean: false # Preserve build artifacts for reuse
      - name: Run Format
        run: |
          cd libs/libclara
          cargo fmt --check
      - name: Run Clippy
        run: |
          cd libs/libclara
          cargo clippy --all-targets --all-features
      - name: Run Build
        run: |
          cd libs/libclara
          cargo build --all-targets --all-features --verbose
      - name: Run Test
        run: |
          cd libs/libclara
          cargo test --all-targets --all-features --verbose
