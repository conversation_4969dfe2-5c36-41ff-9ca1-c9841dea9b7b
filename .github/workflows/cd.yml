name: CD

on:
  workflow_dispatch:
    inputs:
      update_daily_run_image:
        description: "update daily-run test image"
        type: boolean
        default: false
      update_dedicated_image:
        description: "update dedicated test image"
        type: boolean
        default: false
      update_cn_image:
        description: create a PR to update CN image
        type: boolean
        default: false
      auto_merge_update_cn_image:
        description: auto merge PR to update CN image
        type: boolean
        default: false

jobs:
  build:
    name: Build Release
    strategy:
      fail-fast: true
      matrix:
        arch: [X64, ARM64]
    runs-on:
      - self-hosted
      - serverless
      - ${{ matrix.arch }}
    timeout-minutes: 60
    env:
      # GitHub Action supports color output
      CLICOLOR_FORCE: "1"
      CMAKE_COLOR_DIAGNOSTICS: ON
      # For dedicated image, use the version v8.5.0
      PRE_DEF_VERSION: ${{ inputs.update_dedicated_image && 'v8.5.0' || '' }}
      CC: clang
      CXX: clang++
    steps:
      - name: Setup minio
        run: |
          mc alias set minio_server ${{ vars.MINIO_ENDPOINT }} ${{ secrets.MINIO_AK }} ${{ secrets.MINIO_SK }}

      - name: Checkout repository and submodules
        uses: actions/checkout@v4
        with:
          submodules: recursive
          ref: ${{ github.event.inputs.branch }}
          token: ${{ secrets.GIT_ACTION_BOT }}
          fetch-depth: 0
          clean: false # Preserve build artifacts for reuse

      - name: Prepare TiFlash Proxy cache
        id: proxy_cache
        run: |
          pushd contrib/tiflash-proxy > /dev/null
          PROXY_COMMIT=$(git log -1 --format=%H)
          popd > /dev/null

          PROXY_CACHE_DIR=minio_server/tiflash-cse-ci/cd_tiflash_proxy_cache
          mc ls $PROXY_CACHE_DIR

          PROXY_CACHE_OBJECT=$PROXY_CACHE_DIR/$PROXY_COMMIT-$(uname -p)-release.so
          echo "PROXY_CACHE_OBJECT=$PROXY_CACHE_OBJECT" | tee -a $GITHUB_OUTPUT

          rm -f /tmp/libtiflash_proxy.so
          mc cp $PROXY_CACHE_OBJECT /tmp/libtiflash_proxy.so || true
          if [ -f /tmp/libtiflash_proxy.so ]
          then
            # Cache exist, we will reuse the TiFlash proxy library.
            echo "USE_INTERNAL_TIFLASH_PROXY=OFF" | tee -a $GITHUB_OUTPUT
            mkdir -p contrib/tiflash-proxy/target/release
            cp /tmp/libtiflash_proxy.so contrib/tiflash-proxy/target/release/libtiflash_proxy.so
          else
            # Cache not exist, we will build a new TiFlash proxy library.
            echo "USE_INTERNAL_TIFLASH_PROXY=ON" | tee -a $GITHUB_OUTPUT
            rm -rf contrib/tiflash-proxy/target/release
            echo "::warning ::TiFlash proxy library cache not found, will build a new one."
          fi

      - name: Configure
        run: |
          mkdir -p cmake-build-Release
          cd cmake-build-Release

          # Always clean up cmake cache for release.
          cmake .. -GNinja --fresh \
            -DCMAKE_BUILD_TYPE=RELWITHDEBINFO \
            -DCMAKE_C_COMPILER=clang \
            -DCMAKE_CXX_COMPILER=clang++ \
            -DENABLE_TESTING=OFF \
            -DENABLE_TESTS=OFF \
            -DENABLE_FAILPOINTS=OFF \
            -DTIFLASH_RELEASE_VERSION=${PRE_DEF_VERSION} \
            -DPREBUILT_LIBS_ROOT=$GITHUB_WORKSPACE/contrib/tiflash-proxy/ \
            -DUSE_INTERNAL_TIFLASH_PROXY=${{ steps.proxy_cache.outputs.USE_INTERNAL_TIFLASH_PROXY }}

      - name: Build
        run: |
          cd cmake-build-Release
          cmake --build . --target tiflash --parallel $(nproc)
          rm -rf artifacts  # Clean up old artifacts. There is no workspace clean up.
          cmake --install . --component=tiflash-release --prefix=artifacts
          ls -la artifacts/

      - name: Print Build Version
        run: |
          cd cmake-build-Release/artifacts
          ./tiflash version

      - name: Cache TiFlash Proxy
        if: steps.proxy_cache.outputs.USE_INTERNAL_TIFLASH_PROXY == 'ON'
        run: |
          mc cp cmake-build-Release/contrib/tiflash-proxy-cmake/release/libtiflash_proxy.so ${{ steps.proxy_cache.outputs.PROXY_CACHE_OBJECT }}

      - name: Login to Harbor
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: ${{ secrets.HARBOR_USER }}
          password: ${{ secrets.HARBOR_TOKEN }}

      - name: Prepare Build Image
        id: image_id
        run: |
          VERSION=${{ github.sha }}-$(dpkg --print-architecture)
          echo "VERSION=$VERSION" | tee -a $GITHUB_OUTPUT

          TAG=hub.pingcap.net/sunxiaoguang/serverless/tiflash:$VERSION
          echo "TAG=$TAG" | tee -a $GITHUB_OUTPUT

      - name: Build and Push Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: release-serverless/Dockerfile
          push: true
          tags: ${{ steps.image_id.outputs.TAG }}

  multi-arch:
    name: Create Multi-Architecture Image
    runs-on:
      - self-hosted
      - serverless
      - X64
    needs: ["build"]
    steps:
      - name: Login to Harbor
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: ${{ secrets.HARBOR_USER }}
          password: ${{ secrets.HARBOR_TOKEN }}
      - name: create-manifest harbor
        run: |
          docker manifest create hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }} \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-amd64 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-arm64
      - name: push-manifest harbor
        run: |
          docker manifest push hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}

      - name: Login to Harbor (keyspace)
        if: ${{ (inputs.update_daily_run_image) || (inputs.update_dedicated_image) }}
        uses: docker/login-action@v3
        with:
          registry: hub.pingcap.net
          username: "robot$keyspace+cdbot"
          password: ${{ secrets.HARBOR_KEYSPACE_TOKEN }}

      - name: set-serverless-additional-tags
        if: ${{ inputs.update_daily_run_image }}
        run: |
          # Remove the local manifest entry if it exists
          docker manifest rm hub.pingcap.net/keyspace/tiflash:v8.5.0 || true

          docker manifest create hub.pingcap.net/keyspace/tiflash:v8.5.0 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-amd64 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-arm64

          docker manifest push hub.pingcap.net/keyspace/tiflash:v8.5.0

      - name: set-dedicated-additional-tags
        if: ${{ inputs.update_dedicated_image }}
        run: |
          # Remove the local manifest entry if it exists
          docker manifest rm hub.pingcap.net/keyspace/tiflash:v8.5.0-cse-dedicated || true

          docker manifest create hub.pingcap.net/keyspace/tiflash:v8.5.0-cse-dedicated \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-amd64 \
          --amend hub.pingcap.net/sunxiaoguang/serverless/tiflash:${{ github.sha }}-arm64

          docker manifest push hub.pingcap.net/keyspace/tiflash:v8.5.0-cse-dedicated

      - name: Update CN image to track this version
        if: ${{ inputs.update_cn_image }}
        uses: benc-uk/workflow-dispatch@v1
        with:
          workflow: update-image-version.yaml
          repo: tidbcloud/tiflash-autoscale-supervisor
          ref: refs/heads/main
          token: ${{ secrets.GIT_ACTION_BOT }}
          inputs: |
            {
              "commit": "${{ github.sha }}",
              "extra_assignee": "${{ github.actor }}",
              "extra_url": "${{github.server_url}}/${{ github.repository }}/actions/runs/${{ github.run_id }}",
              "auto_approve": "${{ inputs.auto_merge_update_cn_image }}"
            }
