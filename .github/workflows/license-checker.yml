name: License checker

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master

jobs:
  check-license:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Check License Header
        uses: apache/skywalking-eyes@v0.3.0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          log: info
          config: .github/licenserc.yml
