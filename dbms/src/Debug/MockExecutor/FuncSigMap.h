// Copyright 2023 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <common/types.h>
#include <tipb/expression.pb.h>

namespace DB::tests
{
extern std::unordered_map<String, tipb::ScalarFuncSig> func_name_to_sig;
extern std::unordered_map<String, tipb::ExprType> agg_func_name_to_sig;
extern std::unordered_map<String, tipb::ExprType> window_func_name_to_sig;
} // namespace DB::tests
