// .cpp autogenerated by cmake

#include <Common/config_build.h>

const char * auto_config_build[]{
    "VERSION_FULL",
    "@VERSION_FULL@",
    "VERSION_DESCRIBE",
    "@VERSION_DESCRIBE@",
    "VERSION_GITHASH",
    "@VERSION_GITHA<PERSON>H@",
    "BUILD_DATE",
    "@BUILD_DATE@",
    "BUILD_TYPE",
    "@CMAKE_BUILD_TYPE@",
    "SYSTEM",
    "@CMAKE_SYSTEM@",
    "SYSTEM_PROCESSOR",
    "@CMAKE_SYSTEM_PROCESSOR@",
    "LIBRARY_ARCHITECTURE",
    "@CMAKE_LIBRARY_ARCHITECTURE@",
    "CMAKE_VERSION",
    "@CMAKE_VERSION@",
    "C_COMPILER",
    "@CMAKE_C_COMPILER@",
    "C_COMPILER_VERSION",
    "@CMAKE_C_COMPILER_VERSION@",
    "CXX_COMPILER",
    "@CMAKE_CXX_COMPILER@",
    "CXX_COMPILER_VERSION",
    "@CMAKE_CXX_COMPILER_VERSION@",
    "C_FLAGS",
    "@FULL_C_FLAGS@",
    "CXX_FLAGS",
    "@FULL_CXX_FLAGS@",
    "LINK_FLAGS",
    "@CMAKE_EXE_LINKER_FLAGS@",
    "BUILD_COMPILE_DEFINITIONS",
    "@BUILD_COMPILE_DEFINITIONS@",
    "BUILD_INCLUDE_DIRECTORIES",
    "@BUILD_INCLUDE_DIRECTORIES@",
    "STATIC",
    "@USE_STATIC_LIBRARIES@",
    "USE_INTERNAL_MEMCPY",
    "@USE_INTERNAL_MEMCPY@",
    "USE_GLIBC_COMPATIBILITY",
    "@GLIBC_COMPATIBILITY@",
    "USE_JEMALLOC",
    "@USE_JEMALLOC@",
    "USE_MIMALLOC",
    "@USE_MIMALLOC@",
    "USE_UNWIND",
    "@USE_UNWIND@",
    "USE_LLVM_LIBUNWIND",
    "@USE_LLVM_LIBUNWIND@",
    "USE_QPL",
    "@USE_QPL@",
    "USE_RE2_ST",
    "@USE_RE2_ST@",
    "USE_VECTORCLASS",
    "@USE_VECTORCLASS@",
    "USE_Poco_NetSSL",
    "@Poco_NetSSL_FOUND@",

    nullptr,
    nullptr};
