// Copyright 2023 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <common/types.h>

#include <ostream>

namespace TiFlashBuildInfo
{
String getName();
/// Semantic version.
String getVersion();
/// Release version that follows PD/TiKV/TiDB convention.
String getReleaseVersion();
String getEdition();
String getGitHash();
String getGitBranch();
String getUTCBuildTime();
String getProfile();
UInt32 getMajorVersion();
UInt32 getMinorVersion();
UInt32 getPatchVersion();

void outputDetail(std::ostream & os);
} // namespace TiFlashBuildInfo
