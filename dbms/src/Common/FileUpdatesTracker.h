// Copyright 2023 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

#include <Poco/File.h>
#include <Poco/Timestamp.h>

#include <string>


class FileUpdatesTracker
{
private:
    std::string path;
    Poco::Timestamp known_time;

public:
    explicit FileUpdatesTracker(const std::string & path_)
        : path(path_)
        , known_time(0)
    {}

    bool isModified() const { return getLastModificationTime() > known_time; }

    void fixCurrentVersion() { known_time = getLastModificationTime(); }

private:
    Poco::Timestamp getLastModificationTime() const { return Poco::File(path).getLastModified(); }
};
