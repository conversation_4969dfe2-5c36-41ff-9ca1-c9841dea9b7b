#pragma once

// .h autogenerated by cmake!

#cmakedefine TIFLASH_VERSION_MAJOR @TIFLASH_VERSION_MAJOR@
#ifndef TIFLASH_VERSION_MAJOR
#define TIFLASH_VERSION_MAJOR 0
#endif
#cmakedefine TIFLASH_VERSION_MINOR @TIFLASH_VERSION_MINOR@
#ifndef TIFLASH_VERSION_MINOR
#define TIFLASH_VERSION_MINOR 0
#endif
#cmakedefine TIFLASH_VERSION_PATCH @TIFLASH_VERSION_PATCH@
#ifndef TIFLASH_VERSION_PATCH
#define TIFLASH_VERSION_PATCH 0
#endif
#cmakedefine TIFLASH_VERSION_EXTRA "@TIFLASH_VERSION_EXTRA@"
#ifndef TIFLASH_VERSION_EXTRA
#define TIFLASH_VERSION_EXTRA ""
#endif

#cmakedefine TIFLASH_NAME "@TIFLASH_NAME@"
#cmakedefine TIFLASH_VERSION "@TIFLASH_VERSION@"
#cmakedefine TIFLASH_RELEASE_VERSION "@TIFLASH_RELEASE_VERSION@"
#cmakedefine TIFLASH_EDITION "@TIFLASH_EDITION@"
#cmakedefine TIFLASH_GIT_HASH "@TIFLASH_GIT_HASH@"
#cmakedefine TIFLASH_GIT_BRANCH "@TIFLASH_GIT_BRANCH@"
#cmakedefine TIFLASH_UTC_BUILD_TIME "@TIFLASH_UTC_BUILD_TIME@"
#cmakedefine TIFLASH_PROFILE "@TIFLASH_PROFILE@"
