// Copyright 2023 PingCAP, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#pragma once

namespace DB
{
struct FormatSettingsJSON
{
    bool force_quoting_64bit_integers = true;
    bool output_format_json_quote_denormals = true;

    FormatSettingsJSON() = default;

    FormatSettingsJSON(bool force_quoting_64bit_integers_, bool output_format_json_quote_denormals_)
        : force_quoting_64bit_integers(force_quoting_64bit_integers_)
        , output_format_json_quote_denormals(output_format_json_quote_denormals_)
    {}
};

} // namespace DB
